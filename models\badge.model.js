const { MEDIA } = require("../config/attributes");
const history = require("../models/plugins/history.plugin");
const media = require("../models/plugins/media.plugin");

module.exports = (sequelize, DataTypes) => {
  const Badge = sequelize.define(
    "Badge",
    {
      badge_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      status: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
      },
      images: {
        type: MEDIA,
        allowNull: true,
      },
      content: {
        type: DataTypes.JSON,
        allowNull: true,
      },
      schema: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      key: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      variables: {
        type: DataTypes.JSON,
        allowNull: true,
      },
      format: {
        type: DataTypes.ENUM('PNG', 'JPEG', 'JPG', 'WEBP', 'BMP', 'TIFF', 'PDF'),
        allowNull: false,
        defaultValue: 'PNG',
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      tableName: "badge",
      timestamps: true,
      underscored: true,
    }
  );

  Badge.associate = (models) => {
    // Define associations here if needed
    // e.g., Badge.hasMany(models.UserBadge, { foreignKey: 'badge_id', as: 'user_badges', onDelete: 'CASCADE' });
  };

  // Attach history and media plugins
  history(Badge, sequelize, DataTypes);
  media(Badge, sequelize, DataTypes);

  return Badge;
};
