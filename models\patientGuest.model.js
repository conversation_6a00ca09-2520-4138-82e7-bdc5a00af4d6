const history = require("../models/plugins/history.plugin");
const { MEDIA } = require("../config/attributes");
const media = require("../models/plugins/media.plugin");

module.exports = (sequelize, DataTypes) => {
  const PatientGuest = sequelize.define(
    "PatientGuest",
    {
      patient_guest_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      first_name: {
        type: DataTypes.STRING(100),
        allowNull: false,
      },
      last_name: {
        type: DataTypes.STRING(100),
        allowNull: false,
      },
      email: {
        type: DataTypes.STRING(255),
        allowNull: true,
        unique: true,
        set(value) {
          this.setDataValue('email', value?.trim() === '' ? null : value);
        },
      },
      phone: {
        type: DataTypes.STRING(20),
        allowNull: true,
      },
      organization: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      guest_type: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: null,
      },
      image: {
        type: MEDIA,
        allowNull: true,
      },
      relationship_type: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      related_person_name: {
        type: DataTypes.STRING(200),
        allowNull: true,
      },
      related_person_contact: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
      relationship_status: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      birth_date:{
        type: DataTypes.DATE,
        allowNull: true,
      },
      is_walkin:{
        type: DataTypes.BOOLEAN,
        defaultValue: true,
      },
      friends_and_family:{
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      is_emergency_contact: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      emergency_contact_priority: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      can_make_decisions: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      has_custody: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      lives_with_patient: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      relationship_notes: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      effective_from: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      effective_to: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      reason: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      denied_on: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      patient_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "patient",
          key: "patient_id",
        },
        onDelete: "CASCADE",
      },
        updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    
    },
    {
      tableName: "patient_guest",
      timestamps: true,
      underscored: true,
    }
  );

  PatientGuest.associate = (models)   => {
    PatientGuest.belongsTo(models.Patient, {
      foreignKey: "patient_id",
      as: "patient",
    });

    PatientGuest.belongsTo(models.MasterData, {
      foreignKey: "guest_type",
      targetKey: "key",
      as: "patient_guest_guest_type_name",
      constraints: false,
      scope: {
        group: "patient_guest_guest_type",
      },
    });
    PatientGuest.belongsTo(models.MasterData, {
      foreignKey: "relationship_type",
      targetKey: "key",
      as: "patient_guest_relation_type_name",
      constraints: false,
      scope: {
        group: "patient_guest_relation_type",
      },
    });
    PatientGuest.belongsTo(models.MasterData, {
      foreignKey: "relationship_status",
      targetKey: "key",
      as: "patient_guest_relationship_status_name",
      constraints: false,
      scope: {
        group: "patient_guest_relationship_status",
      },
    });
  };

  history(PatientGuest, sequelize, DataTypes);
  media(PatientGuest, sequelize, DataTypes);
  
  return PatientGuest;
};
