const { MEDIA } = require("../config/attributes");
const history = require("../models/plugins/history.plugin");
const media = require("../models/plugins/media.plugin");

module.exports = (sequelize, DataTypes) => {
  const Watchlist = sequelize.define(
    "Watchlist",
    {
      watchlist_id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      // added_by: {
      //   type: DataTypes.UUID,
      //   allowNull: false,
      //   references: {
      //     model: "identity",
      //     key: "eid",
      //   },
      //   onDelete: "CASCADE",
      // },
      first_name: {
        type: DataTypes.STRING(50),
        allowNull: false,
      },
      middle_name: {
        type: DataTypes.STRING(50),
        allowNull: true,
      },
      last_name: {
        type: DataTypes.STRING(50),
        allowNull: false,
      },
      suffix: {
        type: DataTypes.STRING(10),
        allowNull: true,
      },
      date_of_birth: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      email:{
        type: DataTypes.STRING(100),
        allowNull: true,
      },
      phone:{
        type: DataTypes.STRING(20),
        allowNull: true,
      },
      host:{
        type: DataTypes.STRING(20),
        allowNull: true,
      },
      address: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      status:{
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      gender:{
        type:DataTypes.STRING(20),
        allowNull:true
      },
      height:{
        type:DataTypes.STRING(20),
        allowNull:true
      },
      weight:{
        type:DataTypes.STRING(20),
        allowNull:true
      },
      hair_color:{
        type:DataTypes.STRING(50),
        allowNull:true
      },
      eyes_color:{
        type:DataTypes.STRING(20),
        allowNull:true
      },
      reason:{
        type:DataTypes.INTEGER,
        allowNull:true
      },
      description:{
        type:DataTypes.STRING(50),
        allowNull:true
      },
      documents:{
        type:MEDIA,
        allowNull:true,
      },
      image: {
        type: MEDIA,
        allowNull: true,
      },
      expiry_date: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      updated_by: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      tableName: "watchlist",
      timestamps: true,
      underscored: true,
    }
  );

  Watchlist.associate = (models) => {
    // Watchlist.belongsTo(models.Identity, {
    //   foreignKey: "identity_id",
    //   as: "identity",
    // });

    Watchlist.belongsTo(models.MasterData, {
      foreignKey: "status",
      targetKey: "key",
      as: "watchlist_status_name",
      constraints: false,
      scope: {
        group: "watchlist_status",
      },
    }); 
    Watchlist.belongsTo(models.MasterData, {
      foreignKey: "reason",
      targetKey: "key",
      as: "watchlist_reason_name",
      constraints: false,
      scope: {
        group: "watchlist_reason",
      },
    }); 

   
  };

  // Apply plugins
  history(Watchlist, sequelize, DataTypes);
  media(Watchlist, sequelize, DataTypes);


  return Watchlist;
};